#ifndef LOGIC_H
#define LOGIC_H

#include "types.h"
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>
#include <limits.h>
#include <stdarg.h>
#include "types.h"


const int dirRows[4] = {-1, 0, 1, 0};  // north, east, south, west
const int dirCol[4]  = {0, 1, 0, -1};


// functions declarations
void load_seed();
int apply_cell_effect(Player *p, const Cell *cell, int *bonus_mul);
void setup_players(GameState *Game);
bool same_pos(Position a, Position b);
void place_flag_randomly(GameState *Game);
void blocked_cost(GameState *Game, Player *P);
char *get_bawana_type_name(BawanaType type);
bool attempt_move(GameState *Game, Player *P, int steps, bool *sent_to_bawana, bool *capture, int *cells_moved, int *cost, int *bonus_mul, bool *wall_block, char *message);
void check_capture(GameState *Game, Player* P, bool *captured);
Cell *check_pole(GameState *Game, Cell *current);
Cell *check_stairs(GameState *Game, Cell *current);
bool can_use_stair(Stair *stair, Position current_pos);
int man_best_distance(Position pos1, Position pos2);
void initialize_poles(GameState *Game);
void initialize_stairs(GameState *Game);
void randomize_stairs_direction(GameState *Game);
void apply_bawana(GameState *Game, Player *p, Cell *cell);
void exit_bawana(Player *p);
Cell *random_bawana_cell(GameState *Game);
void initialize_bawana(GameState *Game);
void consumables_randomize(GameState *Game);
void add_wall(GameState *Game, int floor, int row1,  int col1, int row2, int col2);
int min(int a, int b);
int max(int a, int b);
void init_cells(GameState *Game);
void init_walls(GameState *Game);
void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul, Coord coordinates);
bool is_in_board (int row, int col);
bool is_cell_available(GameState *Game, int floor, int row, int col);
bool is_cell_available_for_structures(GameState *Game, int floor, int row, int col);
Cell* get_cell (GameState *Game, int floor, int row, int col);
int set_direction(int die);
int rand_int(int low, int high);
bool is_valid_direction(int d);
char* direction_name(Direction dir);
void print_stair_dir(GameState *Game);
void print_debug_out(GameState *Game);
void load_stairs(GameState *Game);
void load_poles(GameState *Game);
void load_walls(GameState *Game);
void load_flag(GameState *Game);
int get_cell_number(Position pos);
void block_stair_passing_cells(GameState *Game, Stair *stair);
bool next_cell_check(GameState *Game, Position next, Player *P);
void apply_pole_or_stair(GameState *Game, Position *current, Player *P, char *message);
void initialize_game(GameState *Game);
void handle_stairs_direction(GameState *Game);
bool handle_food_poison(GameState *Game, Player *P, char *message);
bool handle_in_maze(GameState *Game, Player *P, int move_die);
void handle_dices(GameState *Game, Player *P, int move_die, char *message);
void move_player(GameState *Game, Player *P, int steps, char *message);
bool check_win(GameState *Game, Player *P);
void play(GameState *Game);
void log_message(char *string, ...);
void error_log(char *string, ...);
void init_logs();
bool can_player_win(GameState *Game, Position flag);


// =================== PLAY GAME ===============================

void play(GameState *Game) {
    initialize_game(Game);
    bool game_over = false;
    log_message("\n\n========================================\n");
    log_message("||           MAZE RUNNER               ||\n");
    log_message("========================================\n\n");
    log_message(" Flag placed at [Floor:%d, Row:%d, Col:%d]\n\n", Game->FLAG.floor, Game->FLAG.row, Game->FLAG.col);

    for (Game->game_round = 0; (GAME_ROUNDS == -1 || Game->game_round < GAME_ROUNDS) && !game_over; Game->game_round++) {
        log_message("\n=========================== ROUND %d ==============================\n", Game->game_round + 1);
        
        handle_stairs_direction(Game);

        for (int player_round = 0; player_round < NUM_PLAYERS; player_round++) {
            Player *P = &Game->PLAYERS[player_round];
                char message[2048] = "";
                log_message("\n------------------------------------------------------------------------------\n");
                log_message("│ Round %3d │ Player %c │ MP: %5d │ Pos: %d of floor %d [%d,%d,%d] │ Dir: %s │\n",
                    Game->game_round + 1, P->name, P->mp,get_cell_number(P->position), P->position.floor, P->position.floor, 
                    P->position.row, P->position.col, direction_name(P->direction));
                log_message("-----------------------------------------------------------------------------\n");


                if (handle_food_poison(Game,P, message)) {
                    continue; 
                }
                int move_die = rand_int(1, 6);
                handle_dices(Game, P,move_die,message);
                if (!handle_in_maze(Game, P, move_die)) {
                    continue; 
                }
                move_player(Game, P, move_die, message);
                if (check_win(Game, P)) {
                    game_over=true;
                    if (STATS) print_debug_out(Game);
                    return;
                }
                
        }
    }

    if (!game_over) {
        if (GAME_ROUNDS == -1) {
            log_message("\nGame ended after %d rounds (infinite mode).\n", Game->game_round);
        } else {
            log_message("\nNo winner within %d rounds.\n", GAME_ROUNDS);
        }
        log_message(" Game ended.\n");
        if (DEBUG) print_debug_out(Game);
    }
}


// ============= INITIALIZATION =======================


void initialize_game(GameState *Game) {
    *Game = (GameState){0};
    init_logs();
    load_seed();
    load_stairs(Game);
    load_poles(Game);
    load_walls(Game);

    init_cells(Game);
    initialize_bawana(Game);
    initialize_poles(Game);
    initialize_stairs(Game);
    init_walls(Game);
    consumables_randomize(Game);
    load_flag(Game);

    setup_players(Game);
}

// ---- file loading ---------

// load stairs from file
void load_stairs(GameState *Game) {
    FILE *file = fopen("stairs.txt", "r");
    if (!file) {
        error_log("Failed to open stairs.txt\n");
        log_message("Failed to open stairs.txt\n");
        exit(1);
    }

    Game->n_stairs = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && Game->n_stairs < CELLS) {
        int s_floor, s_row, s_col, e_floor, e_row, e_col;
        if (sscanf(line, "[%d,%d,%d,%d,%d,%d]",
                   &s_floor, &s_row, &s_col, &e_floor, &e_row, &e_col) == 6) {
            Game->STAIRS[Game->n_stairs].s_floor = s_floor;
            Game->STAIRS[Game->n_stairs].s_row   = s_row;
            Game->STAIRS[Game->n_stairs].s_col   = s_col;
            Game->STAIRS[Game->n_stairs].e_floor = e_floor;
            Game->STAIRS[Game->n_stairs].e_row   = e_row;
            Game->STAIRS[Game->n_stairs].e_col   = e_col;
            Game->STAIRS[Game->n_stairs].mode = BI;
            Game->n_stairs++;
        }
    }
    fclose(file);
    log_message("Loaded %d stairs\n", Game->n_stairs);
    if (Game->n_stairs == 0) {
        error_log("No stairs loaded.\n");
        log_message("No stairs loaded\n");
    }

}

void load_poles(GameState *Game) {
    FILE *file = fopen("poles.txt", "r");
    if (!file) {
        error_log("Failed to open poles.txt\n");
        exit(1);
    }

    Game->n_poles = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && Game->n_poles < CELLS) {
        int s_floor, s_row, s_col, e_floor;
        if (sscanf(line, "[%d,%d,%d,%d]",
                   &s_floor, &e_floor, &s_row, &s_col) == 4) {
            Game->POLES[Game->n_poles].s_floor = s_floor;
            Game->POLES[Game->n_poles].row     = s_row;
            Game->POLES[Game->n_poles].col     = s_col;
            Game->POLES[Game->n_poles].e_floor = e_floor;
            Game->n_poles++;
        }
    }
    fclose(file);
    log_message("Loaded %d poles\n", Game->n_poles);
}

void load_walls(GameState *Game) {
    FILE *file = fopen("walls.txt", "r");
    if (!file) {
        error_log("Could not open walls.txt\n");
        exit(1);
    }

    Game->n_walls = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && Game->n_walls < CELLS) {
        int floor, s_row, s_col, e_row, e_col;
        if (sscanf(line, "[%d,%d,%d,%d,%d]",
                   &floor, &s_row, &s_col, &e_row, &e_col) == 5) {
            Game->WALLS[Game->n_walls].floor = floor;
            Game->WALLS[Game->n_walls].s_row = s_row;
            Game->WALLS[Game->n_walls].s_col = s_col;
            Game->WALLS[Game->n_walls].e_row = e_row;
            Game->WALLS[Game->n_walls].e_col = e_col;
            Game->n_walls++;
        }
    }
    fclose(file);
    log_message("Loaded %d walls\n", Game->n_walls);
}

// load flag from flag.txt or place randomply if failed to load
void load_flag(GameState *Game) {
    FILE *file = fopen("flag.txt", "r");
    if (!file) {
        error_log("Can't open flag.txt\n");
        place_flag_randomly(Game);
        log_message("Flag Placed Randomly\n");
        return;
    }

    char line[256];
    if (fgets(line, sizeof line, file)) {
        int floor, row, col;
        if (sscanf(line, "[%d,%d,%d]", &floor, &row, &col) == 3) {
            if (is_cell_available(Game, floor, row, col)) {
                Game->FLAG.floor = floor;
                Game->FLAG.row   = row;
                Game->FLAG.col   = col;
                log_message("flag loaded from flag.txt: [%d,%d,%d]\n", floor, row, col);
                fclose(file);
                if (can_player_win(Game, Game->FLAG)){
                    log_message("Flag placed at [%d,%d,%d] is reachable\n", floor, row, col);
                } else {
                    error_log("Flag placed at [%d,%d,%d] is not reachable\n", floor, row, col);
                    place_flag_randomly(Game);
                    log_message("Flag Placed Randomly\n");
                    error_log("Flag Placed Randomly\n");
                }
                return;
            } else {
                if (INFO) log_message("Flag cell [%d,%d,%d] specified in flag.txt is not in maze or occupied by stair or pole\n");
                error_log("Flag cell [%d,%d,%d] specified in flag.txt is not in maze or occupied by stair or pole\n");
            }
        }
    }
    fclose(file);
    place_flag_randomly(Game);
    log_message("Flag Placed Randomly\n");
}

// based on 0th floor [0,0,0] is the 1st cell. 
int get_cell_number(Position pos) {
    return pos.row * COLUMNS + pos.col + 1;
}

// since only 3 floors
void add_stair_blocking_wall(GameState *Game, int floor, int row, int col) {
    if (!is_in_board(row, col)) {
        error_log("Stair blocking wall [%d,%d,%d] skipped due to cell not in range\n", floor, row, col);
        return;
    }
    Game->BOARD[floor][row][col].kind = WALL;
    if (INFO) log_message("Added stair blocking wall at [%d,%d,%d]\n", floor, row, col);
}

void block_stair_passing_cells(GameState *Game, Stair *stair) {
    int dif_floors = abs(stair->e_floor-stair->s_floor);
    if (dif_floors <= 1) return;
    int diff_row = abs(stair->e_row-stair->s_row);
    int diff_col = abs(stair->e_col-stair->s_col);
    int row = min(stair->e_row,stair->s_row)+(diff_row)/2;
    int col = min(stair->e_col,stair->s_col)+(diff_col)/2;
    add_stair_blocking_wall(Game, 1, row, col);
}
void load_seed(){
    FILE *file = fopen("seed.txt","r");
    unsigned int seed;
    if (!file){
        error_log("Can't open seed.txt, using random seed\n");
        srand((unsigned)time(NULL));
        return;
    }
    if(fscanf(file, "%u", &seed) == 1) {
        srand(seed);
        log_message("seed %u\n", seed);
        fclose(file);
        return;
    }
    fclose(file);
    srand((unsigned)time(NULL));
    error_log("Invalid seed format in seed.txt\n");
    log_message("Using random seed\n");
}

// -------------------

// initial cell settings
void init_cells(GameState *Game){
    for (int f=0; f<FLOORS; f++) {
        for (int r = 0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++){
                Cell *cell = &Game->BOARD[f][r][c];
                Coord coordinates;
                coordinates.col =c;
                coordinates.row = r;
                coordinates.floor = f;
                if (f==0){
                    if(r>=START_AREA_ROW_START && r <= START_AREA_ROW_END && c >= START_AREA_COL_START && c<=START_AREA_COL_END){
                        set_cell(cell, START, 0, 0, 1,coordinates);
                        continue;
                    }
                    if (r>=BAWANA_ROW_START && r <= BAWANA_ROW_END && c >= BAWANA_COL_START && c <= BAWANA_COL_END){
                        set_cell(cell, BAWANA, 0, 0, 1,coordinates);
                        continue;
                    }
                    if (r == BAWANA_ROW_START-1 && c >= BAWANA_COL_START-1 && c <= BAWANA_COL_END) {
                        set_cell(cell, WALL, 0, 0, 1,coordinates);
                        continue;
                    }
                    if (r >= BAWANA_ROW_START && r <= BAWANA_ROW_END &&  c == BAWANA_COL_START-1) {
                        set_cell(cell, WALL, 0, 0, 1,coordinates);
                        continue;
                    }
                }
                if (f==1){
                    bool in_left   = (c >= F1_RECT1_COL_START && c <= F1_RECT1_COL_END);          // 0..7
                    bool in_right  = (c >= F1_RECT2_COL_START && c <= F1_RECT2_COL_END);    // 16..24
                    bool in_bridge = (r >= F1_BRIDGE_ROW_START && r <= F1_BRIDGE_ROW_END &&
                                    c >= F1_BRIDGE_COL_START && c <= F1_BRIDGE_COL_END);        // 6..9, 8..16
                    if (!(in_left || in_right || in_bridge)) {
                        set_cell(cell, CELL_NONE, 0, 0, 1, coordinates);
                        continue;
                    }
                }

                if (f==2){
                    if(!(c >= F2_RECT_COL_START && c<=F2_RECT_COL_END)){
                        set_cell(cell,CELL_NONE,0,0,1,coordinates);
                        continue;
                    }
                }
                set_cell(cell,NORMAL, 0, 0, 1,coordinates);
            }
        }
    }
}

// player initialization
void initialize_player(Player *player, char name, int start_row, int start_col, int first_row, int first_col, Direction dir){
    player->name = name;
    player->position = (Position) {0,start_row,start_col};
    player->init_pos = (Position) {0,start_row,start_col};
    player->start_cell = (Position) {0, first_row, first_col};
    player->direction = dir;
    player->start_direction = dir;
    player->in_maze = false;
    player->mp = 100;
    player->throws_since_dir_change=0;
    player->turns_skipped=0;
    player->disoriented_left=0;
    player->triggered_left=0;
}

// setting up actual players
void setup_players(GameState *Game) {
    initialize_player(&Game->PLAYERS[0],'A',6,12,5,12,NORTH);
    initialize_player(&Game->PLAYERS[1],'B',9,8,9,7,WEST);
    initialize_player(&Game->PLAYERS[2],'C',9,16,9,17,EAST);

}


// bawana initialization
// cells get type BAWANA and bawana val of mp to gain
// bawana wall goes outwards
void initialize_bawana(GameState *Game) {
    int added_cells = 0;

    //initialize all bawana cells to POINTS
    for (int r = BAWANA_ROW_START; r <= BAWANA_ROW_END; r++) {
        for (int c = BAWANA_COL_START; c <= BAWANA_COL_END; c++) {
            Cell *cell = get_cell(Game, 0, r, c);
            if (!cell || cell->kind != BAWANA) continue;
            cell->bawana_type = POINTS;
            cell->bawana_points = rand_int(10,100);
        }
    }

    // food poisoning
    int i = 0;
    while (i < FOOD_P_CELLS && added_cells < BAWANA_CELLS) {
        Cell *random_cell = random_bawana_cell(Game);
        if (!random_cell) continue;
        if (random_cell->kind != BAWANA || random_cell->bawana_type != POINTS) continue;
        random_cell->bawana_type = FOOD_P;
        if (INFO) log_message("Bawana cell [%d,%d,%d] got bawana effect %s\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, get_bawana_type_name(random_cell->bawana_type));
        i++;
        added_cells++;
    }

    // triggered
    i = 0;
    while (i < TRIGGER_CELLS && added_cells < BAWANA_CELLS) {
        Cell *random_cell = random_bawana_cell(Game);
        if (!random_cell) continue;
        if (random_cell->kind != BAWANA || random_cell->bawana_type != POINTS) continue;
        random_cell->bawana_type = TRIG;
        random_cell->bawana_points = TRIGGERED_POINTS;
        if (INFO) log_message("Bawana cell [%d,%d,%d] got bawana effect %s\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, get_bawana_type_name(random_cell->bawana_type));
        i++;
        added_cells++;
    }

    // disoriented
    i = 0;
    while (i < DISORIENTED_CELS && added_cells < BAWANA_CELLS) {
        Cell *random_cell = random_bawana_cell(Game);
        if (!random_cell) continue;
        if (random_cell->kind != BAWANA || random_cell->bawana_type != POINTS) continue;
        random_cell->bawana_type = DISOR;
        random_cell->bawana_points = DISORIENTED_POINTS;
        if (INFO) log_message("Bawana cell [%d,%d,%d] got bawana effect %s\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, get_bawana_type_name(random_cell->bawana_type));
        i++;
        added_cells++;
    }

    // happy
    i = 0;
    while (i < HAPPY_CELLS && added_cells < BAWANA_CELLS) {
        Cell *random_cell = random_bawana_cell(Game);
        if (!random_cell) continue;
        if (random_cell->kind != BAWANA || random_cell->bawana_type != POINTS) continue;
        random_cell->bawana_type = HAPPY;
        random_cell->bawana_points = HAPPY_POINTS;
        if (INFO) log_message("Bawana cell [%d,%d,%d] got bawana effect %s\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, get_bawana_type_name(random_cell->bawana_type));
        i++;
        added_cells++;
    }
    if (INFO) log_message("Added %d bawana cells with special times other 4 got points\n", added_cells);
}



// checks wall cells available or not and add to cell from walls array
// reject diagonal walls and solso block middle cells
void add_wall(GameState *Game, int floor, int row1,  int col1, int row2, int col2) {
    if (!(is_in_board(row1, col1)) || !(is_in_board(row2,col2))){
        error_log("Wall [%d,%d,%d,%d,%d] skipped due to cells not in range\n", floor, row1,col1,row2,col2);
        if (INFO) log_message("Wall [%d,%d,%d,%d,%d] skipped due to cells not in range\n", floor, row1,col1,row2,col2);
        return;
    }
    if (!((row1==row2) || (col1==col2))) {
        error_log("Wall [%d,%d,%d,%d,%d] skipped due to rows and columns do not match\n", floor, row1,col1,row2,col2);
        if (INFO) log_message("Wall [%d,%d,%d,%d,%d] skipped due to rows and columns do not match\n", floor, row1,col1,row2,col2);
        return;
    }
    
    if (row1==row2){
        for (int i = min(col1,col2); i <= max(col1, col2); i++){
            Game->BOARD[floor][row1][i].kind = WALL;
        }
        if (INFO) log_message("Added horizontal wall from col %d to %d on row %d floor %d\n", min(col1,col2), max(col1,col2), row1, floor);
    }
    if (col1==col2){
        for (int i = min(row1,row2); i <= max(row1, row2); i++){
            Game->BOARD[floor][i][col1].kind = WALL;
        }
        if (INFO) log_message("Added vertical wall from row %d to %d on col %d floor %d\n", min(row1,row2), max(row1,row2), col1, floor);
    }
}

// go through walls array and add one by one
void init_walls(GameState *Game){
    for (int i=0; i < Game->n_walls; i++){
        add_wall(Game, Game->WALLS[i].floor,Game->WALLS[i].s_row,Game->WALLS[i].s_col, Game->WALLS[i].e_row, Game->WALLS[i].e_col);
    }
}


// put available cells to an array and randomly pick cell and assign consumable value
void consumables_randomize(GameState *Game){
    Cell *cells[CELLS];
    int n_active = 0;
    for (int f=0; f < FLOORS; f++) {
        for (int r=0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++) {
                if (!(is_cell_available(Game, f,r,c))) continue;
                cells[n_active++] = &Game->BOARD[f][r][c];
            }
        }
    }

    int con_none = (int) (0.25*n_active);
    int con_1 = (int) (0.35*n_active);
    int bonus_12 = (int) (0.25*n_active);
    int bonus_35 = (int) (0.10*n_active);
    int mul_23 = n_active - (con_none + con_1 + bonus_12 + bonus_35);


    int added_cells =0;
    #define EMPTY(random_cell) ((random_cell)->consumable == 0 && (random_cell)->bonus_add == 0 && (random_cell)->bonus_mul == 1)
    // consumables 0
    int i = 0;
    while (i < con_none && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable =0;
        i++, added_cells++; 
    }
    // consumable 1-4
    i=0;

    while (i < con_1 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable = rand_int(1,4);
        if (INFO) log_message("Cell [%d,%d,%d] got consumable %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->consumable);
        i++, added_cells++; 
    }
    
    // bonus 1,2
    i=0;
    while (i < bonus_12 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(1,2);
        if (INFO) log_message("Cell [%d,%d,%d] got bonus %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_add);
        i++, added_cells++; 
    }

    // bonus 3..5
    i=0;
    while (i < bonus_35 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(3,5);
        if (INFO) log_message("Cell [%d,%d,%d] got bonus %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_add);
        i++, added_cells++; 
    }
    // mul 2,3
    i=0;
    while (i < mul_23 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_mul = rand_int(2,3);
        if (INFO) log_message("Cell [%d,%d,%d] got bonus multiplier %dx\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_mul);
        i++, added_cells++; 
    }

}



// check the stairs loaded into STAIRS array actuakky kead to valid positions and add stair to stair.end_cell and stair.start_cell
// if 2 stairs are full ignore the stair
void initialize_stairs(GameState *Game) {
    for (int i = 0; i < Game->n_stairs; i++) {
        Stair *stair = &Game->STAIRS[i];
        if (!(is_cell_available_for_structures(Game, stair->e_floor, stair->e_row, stair->e_col) && is_cell_available_for_structures(Game, stair->s_floor, stair->s_row, stair->s_col))) {
            if (INFO) log_message("INFO: Stair [%d,%d,%d,%d,%d,%d] skipped\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            error_log("Stair [%d,%d,%d,%d,%d,%d] skipped - invalid start and end cell positions\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            continue;
        }
        if (stair->s_floor == stair->e_floor && stair->s_row == stair->e_row && stair->s_col == stair->e_col) {
            if (INFO) log_message("INFO: Stair [%d,%d,%d,%d,%d,%d] skipped\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            error_log("Stair [%d,%d,%d,%d,%d,%d] skipped - start and end cell are same\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            continue;
        }
        if (stair->s_floor == stair->e_floor) {
            if (INFO) log_message("INFO: Stair [%d,%d,%d,%d,%d,%d] skipped\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            error_log("Stair [%d,%d,%d,%d,%d,%d] skipped - stair is on the same floor, start and end cell\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            continue;
        }

        Cell *start_cell = get_cell(Game, stair->s_floor, stair->s_row, stair->s_col);
        Cell *end_cell = get_cell(Game, stair->e_floor, stair->e_row, stair->e_col);
        if (((stair->s_floor == stair->e_floor && stair->s_row == stair->e_row && stair->s_col == stair->e_col) || !start_cell || !end_cell || (start_cell->num_stairs>=2) )){
            error_log("Stair [%d,%d,%d,%d,%d,%d] skipped - %s\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col, (start_cell->num_stairs>=2) ? "start cell has 2 stairs" : "invalid cell positions");
            continue;
        }
        end_cell->stair[end_cell->num_stairs++] = stair;
        start_cell->stair[start_cell->num_stairs++] = stair;
        block_stair_passing_cells(Game, stair);
        if (INFO) log_message("Stair [%d,%d,%d,%d,%d,%d] added\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
    }
}


// check the poles loaded into POLES array actuakky kead to valid positions and add pole to pole.end_cell and pole.start_cell and pole passing mid cells
void initialize_poles(GameState *Game) {
    for (int i = 0; i < Game->n_poles; i++) {
        Pole *pole = &Game->POLES[i];
        bool valid = true;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(Game, floor, pole->row, pole->col);
            if (cell == NULL || !is_cell_available_for_structures(Game, cell->coord.floor,cell->coord.row,cell->coord.col)){
                valid = false;
                if (INFO) log_message("Pole [%d,%d,%d,%d] not valid skipped\n", pole->s_floor, pole->e_floor, pole->row, pole->col);
                error_log("Pole [%d,%d,%d,%d] skipped - invalid cell positions\n", pole->s_floor, pole->e_floor, pole->row, pole->col);
                break;
            }
        }
        if (!valid) continue;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(Game, floor, pole->row, pole->col);
            if (cell != NULL) {
                if (cell->pole != NULL) {
                    if (pole->s_floor <= cell->pole->s_floor && pole->e_floor >= cell->pole->e_floor){
                        cell->pole = pole;
                    }

                } else {
                    cell->pole = pole;
                }
            }
        }
        if (INFO) log_message("Pole [%d,%d,%d,%d] added\n", pole->s_floor, pole->e_floor, pole->row, pole->col);

    }
}

//=============================================================

// =========================== PLAYER MOVEMENTS ======================

// apply poles or stairs in current cell. updates the message
// check for self captures. if a pole or stair is to bawana apply bawana effects
// actual pole stair movement handled here
void apply_pole_or_stair(GameState *Game, Position *current, Player *P, char *message) {
    Cell *cell_c = get_cell(Game, current->floor, current->row, current->col);
    if (cell_c == NULL) return;
    Cell *jumped = check_pole(Game, cell_c); // prioritize pole
    if (jumped) {
        Game->debug_poles_taken++;
        sprintf(message, "%s%c is now at %d which is a pole cell.\n%c slides down and now placed at %d in floor %d\n",
                message,P->name, get_cell_number((Position){current->floor, current->row, current->col}),
                P->name, get_cell_number((Position){jumped->coord.floor, jumped->coord.row, jumped->coord.col}),
                jumped->coord.floor);

        if (jumped->kind==START){
            P->in_maze=false;
            P->position=P->init_pos;
            P->direction=P->start_direction;
            *current = P->init_pos;  
            sprintf(message,"%s%c sent to starting position because %c took a pole to starting area\n",message, P->name, P->name);
            return;
        } else if (jumped->kind==BAWANA) {
            P->position = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
            apply_bawana(Game, P, jumped);
            return;
        }

        *current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
    } else {
        jumped = check_stairs(Game, cell_c);
        if (jumped) {
            Game->debug_stairs_taken++;
            sprintf(message, "%s%c is now at %d which is a stair cell.\n%c takes the stairs and now placed at %d in floor %d\n",message, P->name,
                    get_cell_number((Position){current->floor, current->row, current->col}),
                    P->name, get_cell_number((Position){jumped->coord.floor, jumped->coord.row, jumped->coord.col}),
                    jumped->coord.floor);

            if (jumped->kind==START){
                P->in_maze=false;
                P->position=P->init_pos;
                P->direction=P->start_direction;
                *current = P->init_pos;  
                sprintf(message,"%s%c sent to starting position because %c took stairs to starting area\n",message, P->name, P->name);
                return;
            } else if (jumped->kind==BAWANA) {
                P->position = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
                apply_bawana(Game, P, jumped);
                return;
            }

            *current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
        }
    }
}

// attempt to move the player. if successful return true. else return false. 
// apply n steps move. actual movement is handled
bool attempt_move(GameState *Game, Player *P, int steps, bool *sent_to_bawana, bool *self_capture, int *cells_moved, int *cost, int *bonus_mult_return, bool *wall_block, char *message) {
    *sent_to_bawana = false;
    *self_capture = false;
    *cells_moved = 0;
    *cost = 0;
    Position start = P->position;
    int mp_start = P->mp;
    Position current = start;
    int remaining = steps;
    int bonus_mul=0;
    int cost_val=0;
    while (remaining > 0) {
        Position next = (Position) {
            current.floor,
            current.row + dirRows[P->direction],
            current.col + dirCol[P->direction]
        };
        if (!next_cell_check(Game, current, P) || !next_cell_check(Game, next, P)){
            P->position = start;
            P->mp = mp_start;
            *cells_moved=0;
            Cell *next_cell = get_cell(Game, next.floor, next.row, next.col);
            if (next_cell != NULL && next_cell->kind == WALL){
                *wall_block = true;
                *cost=-2;
            }
            return false;
        }
        current = next;

        apply_pole_or_stair(Game, &current, P, message);

        if (!P->in_maze) {
            return true;
        }

        Cell *current_cell = get_cell(Game, current.floor, current.row, current.col);
        if (current_cell != NULL) {
            cost_val += apply_cell_effect(P, current_cell, &bonus_mul);
        }
        (*cells_moved)++;
        if (check_win(Game, P)) return true; // win if he passes

        if (P->mp <= 0) {
            log_message("%c movement points are depleted and requires replenishment. Transporting to Bawana.\n", P->name);
            P->position = current;
            apply_bawana(Game, P, NULL);
            *sent_to_bawana=true;
            return true;
        }
        remaining--;

    }
    if (bonus_mul > 1) {
        cost_val *= bonus_mul;
        *bonus_mult_return = bonus_mul;
    }
    *cost += cost_val;
    P->mp += cost_val;
    // self loop check
    if (same_pos(current, start)) {
        *self_capture = true;
        return false;
    }
    P->position = current;
    return true;
}



// move player by n steps. handles captures, self loops, handle wall blocks reduce mp  and print messages and actually moves the  player
// actual movement is handled here
void move_player(GameState *Game, Player *P, int steps, char *message) {
    bool bawana = false, self_capture = false;
    Position before = P->position;
    int mp_before = P->mp;
    int cells_moved = 0;
    int cost = 0;
    bool wall_block=false;
    int bonus_mul=0;
    bool move = attempt_move(Game, P, steps, &bawana, &self_capture, &cells_moved, &cost, &bonus_mul, &wall_block, message);
    
    if (self_capture) {
        P->in_maze = false;
        P->position=P->init_pos;
        P->direction=P->start_direction;
        log_message("Player %c rolled %d on the movement dice and stuck on a loop and captured self and sent to start position\n", P->name , steps);
    } else if (!move) {
        log_message("%c rolls and %d on the movement dice and cannot move in the %s. Player remains at cell %d of floor %d[%d,%d,%d]\n",
               P->name, steps, direction_name(P->direction), get_cell_number(P->position),P->position.floor, P->position.floor, P->position.row, P->position.col);
        P->position = before;
        P->mp = mp_before;
        if (wall_block) {
            blocked_cost(Game, P);
            return;
        } else {
            log_message("%c moved 0 that cost 0 movement points and is left with %d and is moving in the %s.\n",
                   P->name, P->mp, direction_name(P->direction));
        }
    } else if (!bawana) {
        log_message("%s%c is now at %d of floor %d [%d,%d,%d].\n", message,P->name, get_cell_number(P->position), P->position.floor, P->position.floor, P->position.row, P->position.col);
        bool captured = false;
        check_capture(Game, P, &captured);
        if (bonus_mul > 1) {
            log_message("%c moved %d steps to cell %d of floor %d [%d,%d,%d] that %s %d movement points and multiplied by %d x multiplier and is left with %d mp and is moving in the %s.\n",
                   P->name, cells_moved, get_cell_number(P->position), P->position.floor, P->position.floor, P->position.row, P->position.col,
                   (cost>0)?"gained":"lost", cost/bonus_mul, bonus_mul, P->mp, direction_name(P->direction));
        } else {
            log_message("%c moved %d steps to cell %d of floor %d [%d,%d,%d] that %s %d movement points and is left with %d mp and is moving in the %s.\n",
                   P->name, cells_moved, get_cell_number(P->position), P->position.floor, P->position.floor, P->position.row, P->position.col,
                   (cost>0)?"gained":"lost", cost, P->mp, direction_name(P->direction));
        }
    }
}

// handles when in starting are handles entering maze when 6 rolled. returns iis it possible to enter or not
// if in starting area placing on starting cell handled here
bool handle_in_maze(GameState *Game, Player *P, int move_die) {
    if (!P->in_maze) {
        if (move_die == 6) {
            log_message("%c is at the starting area and rolls 6 on the movement dice and is placed on %d of the maze.\n",
                   P->name, P->start_cell.floor * 100 + P->start_cell.row * 10 + P->start_cell.col);
            P->position = P->start_cell;
            P->in_maze = true;
            P->direction = P->start_direction;
            Cell *start_cell = get_cell(Game, P->start_cell.floor, P->start_cell.row, P->start_cell.col);
            int cost_val = 0;
            int bonus_mul=1;
            if (start_cell != NULL) {
                cost_val += apply_cell_effect(P, start_cell, &bonus_mul);
                P->mp += cost_val;
                if (cost_val) {
                    log_message("Player %c gathered %d movement points entering maze and is now at %d.\n", P->name, cost_val, P->mp);
                }
            }
            P->throws_since_dir_change = (P->throws_since_dir_change + 1) % 4;
            return false;
        } else {
            log_message("%c is at the starting area and rolls %d on the movement dice cannot enter the maze.\n",
                   P->name, move_die);
            return false;
        }
    }
    return true;
}

// handle bawana effects. throws direction die at required rounds set player direction and update the message.
// checks dies before the movement
// actual movement direction handled here.
void handle_dices(GameState *Game, Player *P, int move_die, char *message) {
    (void)Game; // Suppress unused parameter warning
    bool will_roll_dir = (P->throws_since_dir_change == 3);
    int dir_face = -1;
    int steps = move_die;

    if (P->disoriented_left > 0) {
        Direction new_dir = (Direction)rand_int(0, 3);
        sprintf(message, "%c rolls and %d on the movement dice and is disoriented and move in the %s and moves %d cells and is placed at the ",
                P->name, move_die, direction_name(new_dir), move_die);
        P->direction = new_dir;
        P->disoriented_left--;
        if (P->disoriented_left == 0) {
            sprintf(message,"%s\n%c has recovered from disorientation.\n",message, P->name);
        }
        P->throws_since_dir_change = (P->throws_since_dir_change + 1) % 4;
    } else if (will_roll_dir) {
        dir_face = rand_int(1, 6);
        int d = set_direction(dir_face);
        if (d != -1) {
            P->direction = (Direction)d;
            sprintf(message, "%c rolls and %d on the movement dice and %s on the direction dice, changes direction to %s and moves %d cells and ",
                    P->name, move_die, direction_name((Direction)d), direction_name(P->direction), move_die);
        } else {
            sprintf(message, "%c rolls and %d on the movement dice and moves %s by %d cells and ",
                    P->name, move_die, direction_name(P->direction), move_die);
        }
        P->throws_since_dir_change = 0;
    } else {
        sprintf(message, "%c rolls and %d on the movement dice and moves %s by %d cells and ",
                P->name, move_die, direction_name(P->direction), move_die);
        P->throws_since_dir_change++;
    }


    // Check if player is triggered (handle both limited and unlimited cases)
    if ((TRIGGERED_THROWS == -1 && P->triggered_left == -1) || (TRIGGERED_THROWS != -1 && P->triggered_left > 0)) {
        steps *= 2;
        if (TRIGGERED_THROWS != -1) {
            P->triggered_left--;
        }
        if (will_roll_dir) {
            sprintf(message, "%c is triggered and rolls and %d on the movement dice and %d on direction die and move in the %s and moves %d cells and is placed at the ",
                    P->name, move_die, dir_face, direction_name(P->direction), steps);
        } else {
            sprintf(message, "%c is triggered and rolls and %d on the movement dice and move in the %s and moves %d cells and is placed at the ",
                    P->name, move_die, direction_name(P->direction), steps);
        }
    }
}


// ------------- bawana ---------------

// apply bawana effect
// accept a bawana cell and apply the bawana effect it has or pick a random cell and apply the bawana effect it has.
// reset triggered and disoriented states when entering bawana 
void apply_bawana(GameState *Game, Player *p, Cell *cell) {
    Game->debug_bawana_visits++;
    Cell *bawana_cell = cell;
    if (!cell || cell->kind != BAWANA) {
        bawana_cell=random_bawana_cell(Game);;
    }
    if (!bawana_cell) return;
    BawanaType type = bawana_cell->bawana_type;
    log_message("%c is place on a %s and effects take place.\n", p->name, get_bawana_type_name(type));
    if (p->mp<0) p->mp=0;

    if ((TRIGGERED_THROWS == -1 && p->triggered_left == -1) || (TRIGGERED_THROWS != -1 && p->triggered_left > 0) || p->triggered_left>0) {
        p->triggered_left = 0;
        log_message("%c is no longer triggered due to entering bawana.\n", p->name);
    }
    if (p->disoriented_left > 0) {
        p->disoriented_left = 0;
        log_message("%c is no longer disoriented due to entering bawana.\n", p->name);
    }
    if (type==FOOD_P) {
        p->turns_skipped=FOOD_P_SKIPS;
        log_message("%c eats from Bawana and have a bad case of food poisoning. Will need three rounds to recover.\n", p->name);
    } else if (type==DISOR) {
        p->mp += DISORIENTED_POINTS;
        p->disoriented_left = DISORIENTED_THROWS;
        exit_bawana(p);
        log_message("%c eats from Bawana and is disoriented and is placed at the entrance of Bawana with 50 movement points.\n", p->name);
    } else if (type==TRIG) {
        p->mp += TRIGGERED_POINTS;
        p->triggered_left = TRIGGERED_THROWS;
        exit_bawana(p);
        log_message("%c eats from Bawana and is triggered due to bad quality of food. %c is placed at the entrance of Bawana with 50 movement points.\n", p->name, p->name);
    } else if (type==HAPPY) {
        p->mp += HAPPY_POINTS;
        exit_bawana(p);
        log_message("%c eats from Bawana and is happy. %c is placed at the entrance of Bawana with 200 movement points.\n", p->name, p->name);
    } else {
        int pts = bawana_cell->bawana_points;
        p->mp += pts;
        exit_bawana(p);
        log_message("%c eats from Bawana and earns %d movement points and is placed at the %d of floor 0.\n", p->name, pts, get_cell_number(p->position));
    }

}

// =====================================================

// =============== OTHER HELPERS ==============


// check if game rounds passed 4 and and change direction of stairs
void handle_stairs_direction(GameState *Game) {
    if (Game->game_round % STAIRS_CHANGE_EVERY_ROUNDS == 0 && Game->game_round != 0) {
        randomize_stairs_direction(Game);
        log_message(" Stairs direction changed\n");
        print_stair_dir(Game);
    }
}


// sending player again to bawana after ending food poisoning
// return true or false based on player has or hasn't food poisoning. if so return true and reduce number
bool handle_food_poison(GameState *Game, Player *P, char *message) {
    if (P->turns_skipped > 0) {
        P->turns_skipped--;
        log_message("%c is still food poisoned and misses the turn.\n", P->name);
        if (P->turns_skipped == 0) {
            sprintf(message, "%c is now fit to proceed from the food poisoning episode and now placed on a ", P->name);
            apply_bawana(Game, P, NULL);
        }
        return true;
    }
    return false;
}



bool check_win(GameState *Game, Player *P) {
    if (P->in_maze && same_pos(P->position, Game->FLAG)) {
        log_message("\n===============================================================\n");
        log_message(" Player %c captured the flag at [%d,%d,%d]!\n", 
               P->name, P->position.floor, P->position.row, P->position.col);
        log_message(" Game Over in %d rounds!\n", Game->game_round + 1);
        log_message("===============================================================\n");
        return true;
    }
    return false;
}

// final status of game
void print_debug_out(GameState *Game) {
    log_message("\n\nDEBUG: total bawana visits: %d\n", Game->debug_bawana_visits);
    log_message("DEBUG: stairs: %d\n", Game->debug_stairs_taken);
    log_message("DEBUG: poles: %d\n", Game->debug_poles_taken);
    log_message("DEBUG: wall blocks: %d\n", Game->debug_wall_blocks);
    log_message("DEBUG: captures: %d\n", Game->debug_captured_players);
    
}

// when stairs change direction print directions they got
void print_stair_dir(GameState *Game) {
    for (int i=0; i < Game->n_stairs; i++){
        log_message("  Stair%d at [%d,%d,%d,%d,%d,%d] to %s\n",i+1,Game->STAIRS[i].s_floor, Game->STAIRS[i].s_row, Game->STAIRS[i].s_col, Game->STAIRS[i].e_floor, Game->STAIRS[i].e_row, Game->STAIRS[i].e_col, (Game->STAIRS[i].mode==UP)? "up" : (Game->STAIRS[i].mode==DOWN) ? "down":"Bi-Directional");
    }
    log_message("\n");
}

// return random int inclusive of bounds
int rand_int(int low, int high) {
    return low + (rand() % (high-low +1));
}

// returns true if direction valid
bool is_valid_direction(int d) {
    return d>=0 && d<=3;
}

char* direction_name(Direction dir) {
    if (!is_valid_direction(dir)) return "Not Valid";
    switch (dir) {
        case NORTH: return "North";
        case EAST: return "East";
        case WEST: return "West";
        case SOUTH: return "South";
    }
}

// set direction according to die.
int set_direction(int die) {
    switch(die) {
        case 2: return NORTH;
        case 3: return EAST;
        case 4: return SOUTH;
        case 5: return WEST;
        default: return -1;
    }
}

// returns string name of bawana type
char *get_bawana_type_name(BawanaType type){
    switch (type)
            {
            case FOOD_P:
                return "bawana food poisoning";
            case POINTS:
                return "bawana normal cell";
            case DISOR:
                return "bawana disoriented cell";
            case TRIG:
                return "bawana triggered cell";
            case HAPPY:
                return "bawana happy cell";
            default:
                return "bawana cell";
            }
}

/*
returns cell pointer taking floor, row, column
*/
Cell* get_cell (GameState *Game, int floor, int row, int col) {
    if (!is_in_board(row, col)) return NULL;
    return &Game->BOARD[floor][row][col];
}

/*
returns true if the cell is within the board (not considering the restricted areas, only outer box)
*/
bool is_in_board (int row, int col) {
    return (row >= 0 && row < ROWS && col >= 0 && col < COLUMNS);
}


// only returns true if the cell type is NORMAL
bool is_cell_available(GameState *Game, int floor, int row, int col) {
    if (!is_in_board(row, col)) return false;
    Cell *cell = get_cell(Game, floor, row, col);
    if (cell->kind==NORMAL) return true;
    return false;
}

// check cell available for stairs and poles
bool is_cell_available_for_structures(GameState *Game, int floor, int row, int col) {
    if (!is_in_board(row, col)) return false;
    Cell *cell = get_cell(Game, floor, row, col);
    if (cell != NULL && (cell->kind==NORMAL || cell->kind==START || cell->kind==BAWANA)) return true;
    return false;
}

// sets the cell properties
void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul, Coord coordinates) {
    cell->kind = kind;
    cell->consumable = consumables;
    cell->bonus_add = add;
    cell->bonus_mul = mul;
    for (int i = 0; i < 2; i++){
        cell->stair[i] = NULL;
    }
    cell->num_stairs=0;
    cell->pole = NULL;
    cell->bawana_type=BAWANA_NONE;
    cell->bawana_points = 0;
    cell->coord = coordinates;
}

Cell *random_bawana_cell(GameState *Game) {
    Cell *rand_cell = get_cell(Game, 0, rand_int(BAWANA_ROW_START, BAWANA_ROW_END), rand_int(BAWANA_COL_START, BAWANA_COL_END));
    if (!(rand_cell!= NULL && rand_cell->kind==BAWANA)) return random_bawana_cell(Game);
    return rand_cell;
}

void exit_bawana(Player *p) {
    p->position= (Position) {0, BAWANA_EXIT_ROW, BAWANA_EXIT_COL};
    p->direction=NORTH;
}

// pick individual stairs in order and change direction randomply
void randomize_stairs_direction(GameState *Game) {
    for (int i=0; i < Game->n_stairs; i++) {
        int random_012 = rand_int(0,2); 
        Game->STAIRS[i].mode = random_012;
    }

}


// calculate the shortest distance between two positions manhatton distance odified
int man_best_distance(Position pos1, Position pos2) {
    return (abs(pos1.floor-pos2.floor)+1) * (abs(pos1.row - pos2.row) + abs(pos1.col - pos2.col));
}

// check if a player can use a stair. return true if available in right direction in current cell
// only called if stairs are found
bool can_use_stair(Stair *stair, Position current_pos) {
    //  start position
    if (current_pos.floor == stair->s_floor &&
        current_pos.row == stair->s_row &&
        current_pos.col == stair->s_col) {
        return (stair->mode == BI || stair->mode == UP);
    }

    // end position
    if (current_pos.floor == stair->e_floor &&
        current_pos.row == stair->e_row &&
        current_pos.col == stair->e_col) {
        return (stair->mode == BI || stair->mode == DOWN);
    }

    return false;
}

// check if a cell has stairs and return the best cell if available
Cell *check_stairs(GameState *Game, Cell *current) {
    if (current->num_stairs == 0) return NULL;
    Position current_pos = {current->coord.floor, current->coord.row, current->coord.col};

    if (DEBUG) log_message("DEBUG:[%d,%d,%d] has %d stairs\n",
                      current_pos.floor, current_pos.row, current_pos.col, current->num_stairs);

    Cell *best_destination = NULL;
    int best_distance = INT_MAX;


    for (int i = 0; i < current->num_stairs; i++) {
        Stair *stair = current->stair[i];

        if (DEBUG) log_message("DEBUG:  Stair %d: [%d,%d,%d] to [%d,%d,%d], mode=%d\n", i,
                          stair->s_floor, stair->s_row, stair->s_col,
                          stair->e_floor, stair->e_row, stair->e_col,
                          stair->mode);

        if (!can_use_stair(stair, current_pos)) {
            if (DEBUG) log_message("DEBUG:cant use %d stair\n", i);
            continue;
        }

        Position dest_pos;
        if (current_pos.floor == stair->s_floor &&
            current_pos.row == stair->s_row &&
            current_pos.col == stair->s_col) {
            // start to end
            dest_pos = (Position){stair->e_floor, stair->e_row, stair->e_col};
            if (DEBUG) log_message("DEBUG: stair st to end [%d,%d,%d]\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col);
        } else if (current_pos.floor == stair->e_floor &&
                   current_pos.row == stair->e_row &&
                   current_pos.col == stair->e_col) {
            //at end to start
            dest_pos = (Position){stair->s_floor, stair->s_row, stair->s_col};
            if (DEBUG) log_message("DEBUG:stair end to start [%d,%d,%d]\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col);
        } else {
            if (DEBUG) log_message("DEBUG:not at either end");
        }

        // stairs lead to samepos
        if (dest_pos.floor == current_pos.floor &&
            dest_pos.row == current_pos.row &&
            dest_pos.col == current_pos.col) {
            continue;
        }

        Cell *dest_cell = get_cell(Game, dest_pos.floor, dest_pos.row, dest_pos.col);
        if (dest_cell == NULL || !is_cell_available(Game, dest_pos.floor, dest_pos.row, dest_pos.col)) {
            continue;
        }

        int distance = man_best_distance(dest_pos, Game->FLAG);


        if (best_destination == NULL || distance < best_distance) {
            best_destination = dest_cell;
            best_distance = distance;
            if (DEBUG) log_message("DEBUG:best dest  [%d,%d,%d] (dist %d)\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col, distance);
        }
    }

    if (DEBUG) {
        if (best_destination != NULL) {
            log_message("DEBUG: stair final [%d,%d,%d] to [%d,%d,%d]\n",
                   current_pos.floor, current_pos.row, current_pos.col,
                   best_destination->coord.floor, best_destination->coord.row, best_destination->coord.col);
        } else {
            log_message("DEBUG: no usable stair at [%d,%d,%d]\n",
                   current_pos.floor, current_pos.row, current_pos.col);
        }
    }

    return best_destination;
}


// for poles 
Cell *check_pole(GameState *Game, Cell *current) {
    if (!current->pole) return NULL;
    if (current->coord.floor > current->pole->s_floor) {
        return get_cell(Game, current->pole->s_floor, current->pole->row, current->pole->col);
    }
    return NULL;
}

// returns the cost of the cell effect. + or - value. also updates bonus_mul with multipliers passed (rule 14)
int apply_cell_effect(Player *p, const Cell *cell, int *bonus_mul){
    if (cell == NULL) return 0;  
    int cost_val = 0;
    if (cell->consumable > 0) {
        if (DEBUG) log_message("DEBUG: %i MP lost(total: %i)\n", cell->consumable,p->mp);
        cost_val = -cell->consumable;
    }
    else if (cell->bonus_add > 0) {
        cost_val = cell->bonus_add;
        if (DEBUG) log_message("DEBUG: %i MP gain(total: %i)\n", cell->bonus_add,p->mp);

    }
    else if (cell->bonus_mul > 1){
        *bonus_mul *= cell->bonus_mul;
        if (DEBUG) log_message("DEBUG: x%i MP multiply(total: %i)\n", cell->bonus_mul,p->mp);

    }
    return cost_val;
}


// check if player captured another player
// actually send the ther player to start position and log the output
void check_capture(GameState *Game, Player* P, bool *captured) {
    *captured = false;
    for (int i = 0; i < NUM_PLAYERS; i++) {
        Player *Q = &Game->PLAYERS[i];
        if (Q == P) continue;
        if (!Q->in_maze) continue;
        if (P->position.floor==Q->position.floor && P->position.row==Q->position.row && P->position.col == Q->position.col) {
            *captured = true;
            log_message("Player %c captures player %c and sent to start position\n", P->name, Q->name);
            Q->in_maze=false;
            Q->direction=Q->start_direction;
            Q->position=Q->init_pos;
            Game->debug_captured_players++;
        }
    }
}

// check if the given cell available to move. if available return true.
bool next_cell_check(GameState *Game, Position next, Player *P){
        Cell *next_cell = get_cell(Game, next.floor, next.row, next.col);
        if ((next_cell == NULL || !is_cell_available(Game, next_cell->coord.floor, next_cell->coord.row, next_cell->coord.col)) || !is_valid_direction(P->direction)) {
            CellKind blocked_cell_kind;
            char *block_type= "due to cell not in maze";
            if (is_in_board(next.row, next.col) && next_cell != NULL) {
                blocked_cell_kind = next_cell->kind;
                switch (blocked_cell_kind)
                {
                    case WALL:
                        Game->debug_wall_blocks++;
                        block_type = "by a Wall";
                        break;
                    case START:
                        block_type = "by the starting area";
                        break;
                    case BAWANA:
                        block_type = "by the bawana area";
                        break;
                    default:
                        break;
                }
            }
            if (DEBUG) log_message("DEBUG: Move blocked in [%d,%d,%d] %s\n", next.floor, next.row, next.col, block_type);
            return false;
        }
        return true;
}

// apply 2 mp cost when blocked by a wall
void blocked_cost(GameState *Game, Player *P) {
    P->mp -= 2;
    log_message("%c moved 0 that cost 2 movement points and is left with %d and is moving in the %s.\n",
           P->name, P->mp, direction_name(P->direction));
    if (P->mp <= 0) {
        log_message("%c movement points are depleted and requires replenishment. Transporting to Bawana.\n", P->name);
        apply_bawana(Game, P, NULL);
    }
}


// place flag randomly if flag not loaded
void place_flag_randomly(GameState *Game) {
    while (true) {
        int floor = rand_int(0,2), row = rand_int(0,9), col = rand_int(0,24);
        if (!is_cell_available(Game, floor,row,col)) continue;
        Cell *cell = get_cell(Game, floor,row, col);
        if (cell->pole || cell->num_stairs > 0) continue;
        Game->FLAG = (Position){floor,row,col};
        if (can_player_win(Game, Game->FLAG)) break;
    }
}

bool same_pos(Position a, Position b) {
    return (a.floor==b.floor && a.row==b.row && a.col==b.col);
}



void init_logs(){
    if (!LOG) return;
    FILE *error_file = fopen("error_log.txt", "w+");
    if (error_file) {
        fclose(error_file);
    }
    FILE *log_file = fopen("log.txt", "w+");
    if (log_file) {
        fclose(log_file);
    }
}

// Log function to log to console or log file
void log_message(char *string, ...) {
    va_list args;
    va_start(args, string);

    if (!LOG) {
        vprintf(string, args);
    }

    if (LOG) {
        FILE *log_file = fopen("log.txt", "a");
        if (log_file) {
            va_start(args, string);
            vfprintf(log_file, string, args);
            fclose(log_file);
        }
    }

    va_end(args);
}

// log errors to console or error_log
void error_log(char *string, ...) {
    va_list args;
    va_start(args, string);

    if (!LOG){
        fprintf(stderr, "ERROR: ");
        vfprintf(stderr, string, args);
    }

    FILE *error_file = fopen("error_log.txt", "a");
    if (error_file) {
        fprintf(error_file, "ERROR: ");
        vfprintf(error_file, string, args);
        fclose(error_file);
    }

    va_end(args);
}


int min(int a, int b) {
    if (a>b) return b;
    return a;
}

int max(int a, int b) {
    if (a>b) return a;
    return b;
}

// check the flag position is reachable or not before placing the flag
bool can_player_win(GameState *Game, Position flag) {
    if (flag.floor == 0) return true;
    int st_to_upperfloor=0;
    for (int i = 0; i < Game->n_stairs; i++) {
        Stair *stair = &Game->STAIRS[i];
        if (stair->s_floor == 2 || stair->e_floor == 2) st_to_upperfloor++;
        if (stair->s_floor == flag.floor || stair->e_floor == flag.floor ) {
            return true;
        }
    }
    if (flag.floor==1) {
        for (int i = 0; i < Game->n_poles; i++) {
            Pole *pole = &Game->POLES[i];
            if (pole->s_floor == 1 || pole->e_floor == 2 ) {
                if (st_to_upperfloor>0) return true;
            }
        }
    }
    return false;
}
#endif